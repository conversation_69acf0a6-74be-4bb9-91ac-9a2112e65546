using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Security.CompanyContext;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Cache Test Controller
    /// Redis cache sistemini test etmek için geliştirme amaçlı controller
    /// Production'da kaldırılabilir
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    // [Authorize] // Development test için geçici olarak kapatıldı
    public class CacheTestController : ControllerBase
    {
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly ILogger<CacheTestController> _logger;

        public CacheTestController(ICacheService cacheService, ICompanyContext companyContext, ILogger<CacheTestController> logger)
        {
            _cacheService = cacheService;
            _companyContext = companyContext;
            _logger = logger;
        }

        /// <summary>
        /// Redis bağlantı testi
        /// </summary>
        /// <returns>Bağlantı durumu</returns>
        [HttpGet("connection-test")]
        public async Task<IActionResult> ConnectionTest()
        {
            try
            {
                var isConnected = _cacheService.IsConnected();
                var pingTime = await _cacheService.PingAsync();
                var statistics = await _cacheService.GetStatisticsAsync();

                return Ok(new
                {
                    success = true,
                    message = "Redis connection test successful",
                    data = new
                    {
                        isConnected,
                        pingTimeMs = pingTime.TotalMilliseconds,
                        statistics = new
                        {
                            totalKeys = statistics.TotalKeys,
                            usedMemoryMB = statistics.UsedMemory / (1024 * 1024),
                            maxMemoryMB = statistics.MaxMemory / (1024 * 1024),
                            hitRatio = statistics.HitRatio,
                            version = statistics.Version,
                            uptimeHours = statistics.Uptime.TotalHours
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Redis connection test failed");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Redis connection test failed",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Basit cache set/get testi
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="value">Cache value</param>
        /// <param name="expiry">Expiry (saniye)</param>
        /// <param name="companyId">Test için company ID (default: 1)</param>
        /// <returns>Test sonucu</returns>
        [HttpPost("simple-test")]
        public async Task<IActionResult> SimpleTest([FromQuery] string key, [FromQuery] string value, [FromQuery] int? expiry = null, [FromQuery] int companyId = 1)
        {
            try
            {
                var cacheKey = CacheKeyHelper.CreateKey(companyId, "test", key);

                // Set
                await _cacheService.SetAsync(cacheKey, value, expiry);
                _logger.LogInformation("Cache set: {Key} = {Value}", cacheKey, value);

                // Get
                var cachedValue = await _cacheService.GetAsync<string>(cacheKey);
                _logger.LogInformation("Cache get: {Key} = {Value}", cacheKey, cachedValue);

                // Exists
                var exists = await _cacheService.ExistsAsync(cacheKey);

                return Ok(new
                {
                    success = true,
                    message = "Simple cache test successful",
                    data = new
                    {
                        companyId,
                        originalKey = key,
                        cacheKey,
                        originalValue = value,
                        cachedValue,
                        exists,
                        expiry
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Simple cache test failed");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Simple cache test failed",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Multi-tenant cache isolation testi
        /// </summary>
        /// <param name="companyId">Test için company ID (default: 1)</param>
        /// <returns>Test sonucu</returns>
        [HttpPost("multi-tenant-test")]
        public async Task<IActionResult> MultiTenantTest([FromQuery] int companyId = 1)
        {
            try
            {
                var currentCompanyId = companyId;
                var testData = new
                {
                    memberId = 123,
                    memberName = "Test Member",
                    companyId = currentCompanyId,
                    timestamp = DateTime.UtcNow
                };

                // Current company cache
                var currentCompanyKey = CacheKeyHelper.CreateKey(currentCompanyId, "member", 123);
                await _cacheService.SetAsync(currentCompanyKey, testData, 300);

                // Simulate different company (manual key creation)
                var otherCompanyKey = CacheKeyHelper.CreateKey(999, "member", 123);
                await _cacheService.SetAsync(otherCompanyKey, new { memberId = 123, memberName = "Other Company Member", companyId = 999 }, 300);

                // Get current company data
                var currentCompanyData = await _cacheService.GetAsync<object>(currentCompanyKey);

                // Get other company data (should be different)
                var otherCompanyData = await _cacheService.GetAsync<object>(otherCompanyKey);

                // Get company statistics
                var companyStats = await _cacheService.GetCompanyStatisticsAsync(currentCompanyId);

                return Ok(new
                {
                    success = true,
                    message = "Multi-tenant cache test successful",
                    data = new
                    {
                        currentCompanyId,
                        currentCompanyData,
                        otherCompanyData,
                        isolation = currentCompanyData != otherCompanyData,
                        companyStats = new
                        {
                            companyStats.CompanyId,
                            companyStats.TotalKeys,
                            companyStats.EstimatedMemoryUsage,
                            companyStats.EntityCounts
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Multi-tenant cache test failed");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Multi-tenant cache test failed",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Pattern-based cache operations testi
        /// </summary>
        /// <param name="companyId">Test için company ID (default: 1)</param>
        /// <returns>Test sonucu</returns>
        [HttpPost("pattern-test")]
        public async Task<IActionResult> PatternTest([FromQuery] int companyId = 1)
        {
            try
            {

                // Test data oluştur
                var testMembers = new[]
                {
                    new { id = 1, name = "Member 1", email = "<EMAIL>" },
                    new { id = 2, name = "Member 2", email = "<EMAIL>" },
                    new { id = 3, name = "Member 3", email = "<EMAIL>" }
                };

                // Cache'e ekle
                foreach (var member in testMembers)
                {
                    var memberKey = CacheKeyHelper.CreateKey(companyId, "member", member.id);
                    await _cacheService.SetAsync(memberKey, member, 300);
                }

                // Pattern ile key'leri al
                var memberPattern = CacheKeyHelper.CreateEntityPattern(companyId, "member");
                var memberKeys = await _cacheService.GetKeysAsync(memberPattern);

                // Company pattern ile tüm key'leri al
                var companyPattern = CacheKeyHelper.CreateCompanyPattern(companyId);
                var allCompanyKeys = await _cacheService.GetKeysAsync(companyPattern);

                // Pattern ile silme testi (sadece member 1'i sil)
                var member1Key = CacheKeyHelper.CreateKey(companyId, "member", 1);
                await _cacheService.RemoveAsync(member1Key);

                // Silme sonrası key'leri tekrar al
                var memberKeysAfterDelete = await _cacheService.GetKeysAsync(memberPattern);

                return Ok(new
                {
                    success = true,
                    message = "Pattern cache test successful",
                    data = new
                    {
                        companyId,
                        memberPattern,
                        companyPattern,
                        memberKeysCount = memberKeys.Count(),
                        allCompanyKeysCount = allCompanyKeys.Count(),
                        memberKeysAfterDeleteCount = memberKeysAfterDelete.Count(),
                        memberKeys = memberKeys.ToArray(),
                        memberKeysAfterDelete = memberKeysAfterDelete.ToArray()
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Pattern cache test failed");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Pattern cache test failed",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Company cache temizleme testi
        /// </summary>
        /// <param name="companyId">Test için company ID (default: 1)</param>
        /// <returns>Test sonucu</returns>
        [HttpDelete("clear-company-cache")]
        public async Task<IActionResult> ClearCompanyCache([FromQuery] int companyId = 1)
        {
            try
            {
                // Önce test data ekle
                var memberKey = CacheKeyHelper.CreateKey(companyId, "member", 1);
                var paymentKey = CacheKeyHelper.CreateKey(companyId, "payment", 1);
                var userKey = CacheKeyHelper.CreateKey(companyId, "user", 1);

                await _cacheService.SetAsync(memberKey, new { name = "Test Member" }, 300);
                await _cacheService.SetAsync(paymentKey, new { amount = 100 }, 300);
                await _cacheService.SetAsync(userKey, new { username = "testuser" }, 300);

                // Silme öncesi key sayısı
                var beforeStats = await _cacheService.GetCompanyStatisticsAsync(companyId);

                // Company cache'i temizle
                var deletedCount = await _cacheService.ClearCompanyCacheAsync(companyId);

                // Silme sonrası key sayısı
                var afterStats = await _cacheService.GetCompanyStatisticsAsync(companyId);

                return Ok(new
                {
                    success = true,
                    message = "Company cache cleared successfully",
                    data = new
                    {
                        companyId,
                        deletedCount,
                        beforeKeyCount = beforeStats.TotalKeys,
                        afterKeyCount = afterStats.TotalKeys,
                        beforeEntityCounts = beforeStats.EntityCounts,
                        afterEntityCounts = afterStats.EntityCounts
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Clear company cache test failed");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Clear company cache test failed",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Cache extension methods testi
        /// </summary>
        /// <param name="companyId">Test için company ID (default: 1)</param>
        /// <returns>Test sonucu</returns>
        [HttpPost("extension-test")]
        public async Task<IActionResult> ExtensionTest([FromQuery] int companyId = 1)
        {
            try
            {
                // Entity test
                var member = new { id = 999, name = "Extension Test Member", email = "<EMAIL>" };
                var memberKey = CacheKeyHelper.CreateKey(companyId, "member", 999);
                await _cacheService.SetAsync(memberKey, member, 300);
                var cachedMember = await _cacheService.GetAsync<object>(memberKey);

                // Collection test
                var memberList = new[] { member, new { id = 998, name = "Member 2", email = "<EMAIL>" } };
                var collectionKey = CacheKeyHelper.CreateCollectionKey(companyId, "member", "active", "list");
                await _cacheService.SetAsync(collectionKey, memberList, 300);
                var cachedList = await _cacheService.GetAsync<object[]>(collectionKey);

                // Pagination test
                var paginationResult = new { data = memberList, totalCount = 2, pageNumber = 1, pageSize = 10 };
                var paginationKey = CacheKeyHelper.CreatePaginationKey(companyId, "member", 1, 10, "test");
                await _cacheService.SetAsync(paginationKey, paginationResult, 300);
                var cachedPagination = await _cacheService.GetAsync<object>(paginationKey);

                return Ok(new
                {
                    success = true,
                    message = "Extension methods test successful",
                    data = new
                    {
                        entityTest = new { original = member, cached = cachedMember },
                        collectionTest = new { original = memberList, cached = cachedList },
                        paginationTest = new { original = paginationResult, cached = cachedPagination }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Extension methods test failed");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Extension methods test failed",
                    error = ex.Message
                });
            }
        }
    }
}
